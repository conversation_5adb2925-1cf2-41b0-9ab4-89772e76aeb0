<!-- filter form start -->
<form id="filter-form" [formGroup]="filterForm" role="form" class="form">

    <!-- Sales Order Number form group start -->
    <div class="form-group">
        <label class="form-control-label" for="field_salesOrderNumber"><strong>{{salesOrderNumber}}</strong></label>
        <!-- Sales Order Number selection start -->
        <ng-multiselect-dropdown id="field_salesOrderNumber" name="salesOrderNumber" [placeholder]="''"
            formControlName="salesOrderNumber" [settings]="dropdownSettingsForSalesOrder" [data]="salesOrderNumberList">
        </ng-multiselect-dropdown>
        <!-- Sales Order Number selection end -->
    </div>
    <!-- Sales Order Number form group end -->

    <!-- serial number form group start -->
    <div class="form-group">
        <label class="form-control-label" for="field_serialNumber"><strong>{{serialNo}}</strong></label>
        <!-- serial number input start -->
        <input class="form-control" type="text" formControlName="serialNumber" />
        <!-- serial number input end -->
        <!-- validation for serialNumber start -->
        <div
            *ngIf="(filterForm.get('serialNumber').touched || filterForm.get('serialNumber').dirty) && filterForm.get('serialNumber').invalid ">
            <div *ngIf="filterForm.get('serialNumber').errors['maxlength']">
                <p class="alert-color">{{small_textBoxMaxCharactersAllowedMessage}}</p>
            </div>
            <div *ngIf="filterForm.get('serialNumber').errors['pattern']">
                <p class="alert-color">{{specialCharacterErrorMessage}}</p>
            </div>
        </div>
        <!-- validation for serialNumber end -->
    </div>
    <!-- serial number form group end -->

    <!-- probe type form group start -->
    <div class="form-group">
        <label class="form-control-label" for="field_probeTypes"
            id="label_probeTypes"><strong>{{probeType}}</strong></label>
        <!-- probe type selection start -->
        <ng-multiselect-dropdown id="field_probeTypes" name="probeTypes" [placeholder]="''" formControlName="probeTypes"
            [settings]="dropdownSettingsForProbeType" [data]="probeTypesList">
        </ng-multiselect-dropdown>
        <!-- probe type selection end -->
    </div>
    <!-- probe type form group end -->

    <!-- probe Preset form group start -->
    <div class="form-group">
        <label class="form-control-label" for="field_probePresets"
            id="label_probePresets"><strong>{{presets}}</strong></label>
        <ng-multiselect-dropdown id="field_probePresets" name="probePresets" [placeholder]="''"
            formControlName="presetType" [settings]="dropdownSettingsPreset" [data]="presetList">
        </ng-multiselect-dropdown>
    </div>
    <!-- probe Preset form group end -->

    <!-- probe features form group start -->
    <div class="form-group">
        <label class="form-control-label" for="field_probeFeatures"
            id="label_probeFeatures"><strong>{{features}}</strong></label>
        <!-- probe features selection start -->
        <ng-multiselect-dropdown id="field_probeFeatures" name="probeFeatures" [placeholder]="''"
            formControlName="probeFeatures" [settings]="dropdownSettingsFeature" [data]="featuresList">
        </ng-multiselect-dropdown>
        <!-- probe features selection end -->
    </div>
    <!-- probe features form group end -->

    <!-- probe Validity Period form group start -->
    <div class="form-group">
        <label class="form-control-label" for="field_validityPeriod"
            id="label_validityPeriod"><strong>{{featureValidityPeriod}}</strong></label>
        <!-- probe Validity Period selection start -->
        <ng-multiselect-dropdown id="field_validityPeriod" name="validityPeriod" [placeholder]="''"
            class="devicePageDeviceType" formControlName="featureValidityPeriod"
            [settings]="dropdownSettingsFeatureValidityPeriod" [data]="featureValidityPeriodList">
        </ng-multiselect-dropdown>
        <!-- probe Validity Period selection end -->
    </div>
    <!-- probe Validity Period form group end -->

    <!-- Customer Name form group start -->
    <div class="form-group">
        <label class="form-control-label" for="field_customerName"><strong>{{customerName}}</strong></label>
        <!-- Customer Name input start -->
        <input class="form-control" type="text" formControlName="customerName" />
        <!-- Customer Name input end -->
        <!-- validation for customerName start -->
        <div
            *ngIf="(filterForm.get('customerName').touched || filterForm.get('customerName').dirty) && filterForm.get('customerName').invalid ">
            <div *ngIf="filterForm.get('customerName').errors['maxlength']">
                <p class="alert-color">{{textBoxMaxCharactersAllowedMessage}}</p>
            </div>
            <div *ngIf="filterForm.get('customerName').errors['pattern']">
                <p class="alert-color">{{specialCharacterErrorMessage}}</p>
            </div>
        </div>
        <!-- validation for customerName end -->
    </div>
    <!-- Customer Name form group end -->

    <!-- countries filter start -->
    <div class="form-group">
        <label class="form-control-label" for="field_countries"><strong>{{country}}</strong></label>
        <ng-multiselect-dropdown name="countries" [placeholder]="''" formControlName="countries"
            [settings]="dropdownSettingsForCountry" [data]="countryList" (onSelect)="onCountrySelect($event)"
            id="probeCountry" (onDeSelect)="onCountryDeSelect()" (onDeSelectAll)="onCountryDeSelect()">
        </ng-multiselect-dropdown>
    </div>
    <!-- countries filter end -->

    <!-------------------------------------------->
    <!-------------------------------------------->
    <!-- probe Product status form group start -->
    <div class="form-group">
        <label class="form-control-label" for="field_productStatus" id="label_productStatus"><strong>
                Status
            </strong></label>
        <!-- probe Product status selection start -->
        <ng-multiselect-dropdown id="field_productStatus" name="productStatus" [placeholder]="''"
            formControlName="productStatus" [settings]="dropdownSettingsForProductStatus" [data]="productStatusList">
        </ng-multiselect-dropdown>
        <!-- probe Product status selection end -->
    </div>
    <!-- probe Product status form group end -->
    <!-------------------------------------------->
    <!-------------------------------------------->

    <!-- Probe lock status start -->
    <div class="form-group">
        <label class="form-control-label" for="field_probeLockState"><strong>
                Locked</strong></label>
        <ng-multiselect-dropdown name="probeLockState" class="devicePageDeviceType" [placeholder]="''"
            formControlName="lockState" [settings]="dropdownSettingsForLockState" [data]="lockUnlockStateList">
        </ng-multiselect-dropdown>
    </div>
    <!-- device lock status end -->


    <!-- device Edit status start -->
    <div class="form-group">
        <label class="form-control-label" for="field_probeEditState"><strong>
                Editable</strong></label>
        <ng-multiselect-dropdown name="probeEditState" class="devicePageDeviceType" [placeholder]="''"
            formControlName="probeEditState" [settings]="dropdownSettingsForEditState" [data]="editStateList">
        </ng-multiselect-dropdown>
    </div>
    <!-- device Edit status end -->

    <!--######################################################-->
    <!--######################################################-->
    <!----------------------Child Card Start-------------------->
    <!--######################################################-->
    <!--######################################################-->
    <div class="card mb-3">
        <div class="card_child">
            <!--##################################################-->

            <!--Search From Historical Data Start-->
            <div class="form-group d-flex" id="switchBtn">
                <div class="form-control-label textHight mr-3">
                    <strong>
                        {{deviceHistoricalData}}
                    </strong>
                </div>
                <div>
                    <label class="switch">
                        <input type="checkbox" formControlName="deviceHistoricalData" id="deviceHistoricalData"
                            (change)="setDeviceHistoricalData($event)">
                        <span class="slider round"></span>
                    </label>
                </div>
            </div>
            <!--Search From Historical Data End-->

            <!------------------------>
            <!-- Device Model start -->
            <div class="form-group">
                <label class="form-control-label" for="field_customerEmail"><strong>{{deviceModel}}</strong></label>
                <input class="form-control" type="text" formControlName="deviceModel" />
                <!-- validation for deviceModel start -->
                <div
                    *ngIf="(filterForm.get('deviceModel').touched || filterForm.get('deviceModel').dirty) && filterForm.get('deviceModel').invalid ">
                    <div *ngIf="filterForm.get('deviceModel').errors['maxlength']">
                        <p class="alert-color">{{small_textBoxMaxCharactersAllowedMessage}}</p>
                    </div>
                    <div *ngIf="filterForm.get('deviceModel').errors['pattern']">
                        <p class="alert-color">{{specialCharacterErrorMessage}}</p>
                    </div>
                </div>
                <!-- validation for deviceModel end -->
            </div>
            <!-- Device Model end -->
            <!------------------------>

            <!------------------------>
            <!-- Manufacturer start -->
            <div class="form-group">
                <label class="form-control-label" for="field_customerEmail"><strong>{{manufacturer}}</strong></label>
                <input class="form-control" type="text" formControlName="manufacturer" />
                <!-- validation for Manufacturer start -->
                <div
                    *ngIf="(filterForm.get('manufacturer').touched || filterForm.get('manufacturer').dirty) && filterForm.get('manufacturer').invalid ">
                    <div *ngIf="filterForm.get('manufacturer').errors['maxlength']">
                        <p class="alert-color">{{small_textBoxMaxCharactersAllowedMessage}}</p>
                    </div>
                    <div *ngIf="filterForm.get('manufacturer').errors['pattern']">
                        <p class="alert-color">{{specialCharacterErrorMessage}}</p>
                    </div>
                </div>
                <!-- validation for Manufacturer end -->
            </div>
            <!-- Manufacturer end -->
            <!------------------------>

            <!-------------------------------------------->
            <!-------------------------------------------->
            <!-- OS Type form group start -->
            <div class="form-group">
                <label class="form-control-label" for="field_osType"
                    id="label_osType"><strong>{{osType}}</strong></label>
                <!-- OS Type selection start -->
                <ng-multiselect-dropdown id="field_osType" name="osType" [placeholder]="''" formControlName="osType"
                    class="devicePageDeviceType" [settings]="dropdownSettingsForOsType" [data]="osTypeList">
                </ng-multiselect-dropdown>
                <!-- OS Type selection end -->
            </div>
            <!-- OS Type form group end -->
            <!-------------------------------------------->
            <!-------------------------------------------->
            <!--##################################################-->
        </div>
    </div>
    <!--######################################################-->
    <!--######################################################-->
    <!----------------------Child Card end-------------------->
    <!--######################################################-->
    <!--######################################################-->

    <hr class="mt-1 mb-2">
    <div class="">
        <!-- search button start -->
        <button class="btn btn-sm btn-orange mr-3" (click)="searchFilteredProbes()" id="searchProbeBtn"
            [disabled]="filterForm.invalid">{{searchBtnText}}</button>
        <!-- search button end -->
        <!-- clear button start -->
        <button class="btn btn-sm btn-orange" (click)="clearFilter()">{{clearBtnText}}</button>
        <!-- clear button end -->
    </div>
</form>
<!-- filter form end -->