import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ToastrService } from 'ngx-toastr';
import { of, throwError } from 'rxjs';
import { ProbeOperationService, ProbeFilterAction } from './probe-operation.service';
import { ProbeApiService } from 'src/app/shared/Service/ProbeService/probe-api.service';
import { ProbeService } from 'src/app/shared/Service/ProbeService/probe.service';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { DownloadService } from 'src/app/shared/util/download.service';
import { ModuleValidationServiceService } from 'src/app/shared/util/module-validation-service.service';
import { KeyValueMappingServiceService } from 'src/app/shared/util/key-value-mapping-service.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { UpdateFeaturesService } from 'src/app/shared/modalservice/update-features.service';
import { CustomerAssociationService } from 'src/app/shared/modalservice/customer-association.service';
import { UpdateProbeTypeService } from 'src/app/shared/modalservice/Probe/update-probe-type.service';
import { DeviceService } from 'src/app/shared/device.service';
import { ProbeListFilterRequestBody } from 'src/app/model/probe/ProbeListFilterRequestBody.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { ProbListResource, ProbDetailResource } from 'src/app/app.constants';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { HttpResponse } from '@angular/common/http';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { CustomerAssociationRequest } from 'src/app/model/customer-association-request';
import { ConfigureLicenceResponse } from 'src/app/model/probe/ConfigureLicenceResponse.model';

describe('ProbeOperationService', () => {
  let service: ProbeOperationService;
  let probeApiServiceSpy: jasmine.SpyObj<ProbeApiService>;
  let probeServiceSpy: jasmine.SpyObj<ProbeService>;
  let salesOrderApiCallServiceSpy: jasmine.SpyObj<SalesOrderApiCallService>;
  let countryCacheServiceSpy: jasmine.SpyObj<CountryCacheService>;
  let commonsServiceSpy: jasmine.SpyObj<CommonsService>;
  let exceptionHandlingServiceSpy: jasmine.SpyObj<ExceptionHandlingService>;
  let toastrServiceSpy: jasmine.SpyObj<ToastrService>;
  let downloadServiceSpy: jasmine.SpyObj<DownloadService>;
  let moduleValidationServiceSpy: jasmine.SpyObj<ModuleValidationServiceService>;
  let keyValueMappingServiceSpy: jasmine.SpyObj<KeyValueMappingServiceService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let confirmDialogServiceSpy: jasmine.SpyObj<ConfirmDialogService>;
  let updateFeaturesServiceSpy: jasmine.SpyObj<UpdateFeaturesService>;
  let customerAssociationServiceSpy: jasmine.SpyObj<CustomerAssociationService>;
  let updateProbeTypeServiceSpy: jasmine.SpyObj<UpdateProbeTypeService>;
  let deviceServiceSpy: jasmine.SpyObj<DeviceService>;

  beforeEach(() => {
    const probeApiSpy = jasmine.createSpyObj('ProbeApiService', [
      'getAllProbes', 'getProbeTypesList', 'getFeaturesList', 'getPresetsList', 'getFilterValue',
      'updateLockState', 'deleteProbes', 'rmaProductStatusForProbe', 'disableProductStatusForProbe',
      'associationProbeWithSalesOrder', 'dowloadSasUriofFeatureLicenseAsync', 'generateCSVFileForProbe',
      'downloadCSVFileForProbe', 'generateCSVFileForProbeHistoricalConnection'
    ]);
    const probeSpy = jasmine.createSpyObj('ProbeService', [
      'getFeaturesListForFilter', 'getPresetsListForFilter', 'probeEditAction'
    ]);
    const salesOrderSpy = jasmine.createSpyObj('SalesOrderApiCallService', ['getSalesOrderNumberList']);
    const countryCacheSpy = jasmine.createSpyObj('CountryCacheService', ['getCountryListFromCache']);
    const commonsSpy = jasmine.createSpyObj('CommonsService', ['checkNullFieldValue', 'getIdsFromArray', 'getSelectedValueFromEnum', 'getSelectedValueFromBooleanKeyValueMapping']);
    const exceptionSpy = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    const toastrSpy = jasmine.createSpyObj('ToastrService', ['info', 'error', 'success']);
    const downloadSpy = jasmine.createSpyObj('DownloadService', ['downloadExportCSV']);
    const moduleValidationSpy = jasmine.createSpyObj('ModuleValidationServiceService', [
      'validateWithEditableWithMultipalRecoard', 'validateWithUserCountryForMultileRecord',
      'validateWithEditStateForSingleRecord', 'validateWithUserCountryForSingleRecord'
    ]);
    const keyValueMappingSpy = jasmine.createSpyObj('KeyValueMappingServiceService', ['enumOptionToList']);
    const permissionSpy = jasmine.createSpyObj('PermissionService', ['getProbPermission']);
    const confirmDialogSpy = jasmine.createSpyObj('ConfirmDialogService', [
      'confirm', 'getBasicModelConfigForDisableAction'
    ]);
    const updateFeaturesSpy = jasmine.createSpyObj('UpdateFeaturesService', [
      'openAssignProbeFeatureModel', 'getAssignProbeBasicModelConfigDetail'
    ]);
    const customerAssociationSpy = jasmine.createSpyObj('CustomerAssociationService', ['openCustomerAssociationPopup']);
    const updateProbeTypeSpy = jasmine.createSpyObj('UpdateProbeTypeService', ['openUpdateProbeTypePopup']);
    const deviceSpy = jasmine.createSpyObj('DeviceService', ['dowloadSasUriofFeatureLicenseConfirmationModel']);

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        ProbeOperationService,
        { provide: ProbeApiService, useValue: probeApiSpy },
        { provide: ProbeService, useValue: probeSpy },
        { provide: SalesOrderApiCallService, useValue: salesOrderSpy },
        { provide: CountryCacheService, useValue: countryCacheSpy },
        { provide: CommonsService, useValue: commonsSpy },
        { provide: ExceptionHandlingService, useValue: exceptionSpy },
        { provide: ToastrService, useValue: toastrSpy },
        { provide: DownloadService, useValue: downloadSpy },
        { provide: ModuleValidationServiceService, useValue: moduleValidationSpy },
        { provide: KeyValueMappingServiceService, useValue: keyValueMappingSpy },
        { provide: PermissionService, useValue: permissionSpy },
        { provide: ConfirmDialogService, useValue: confirmDialogSpy },
        { provide: UpdateFeaturesService, useValue: updateFeaturesSpy },
        { provide: CustomerAssociationService, useValue: customerAssociationSpy },
        { provide: UpdateProbeTypeService, useValue: updateProbeTypeSpy },
        { provide: DeviceService, useValue: deviceSpy }
      ]
    });

    service = TestBed.inject(ProbeOperationService);
    probeApiServiceSpy = TestBed.inject(ProbeApiService) as jasmine.SpyObj<ProbeApiService>;
    probeServiceSpy = TestBed.inject(ProbeService) as jasmine.SpyObj<ProbeService>;
    salesOrderApiCallServiceSpy = TestBed.inject(SalesOrderApiCallService) as jasmine.SpyObj<SalesOrderApiCallService>;
    countryCacheServiceSpy = TestBed.inject(CountryCacheService) as jasmine.SpyObj<CountryCacheService>;
    commonsServiceSpy = TestBed.inject(CommonsService) as jasmine.SpyObj<CommonsService>;
    exceptionHandlingServiceSpy = TestBed.inject(ExceptionHandlingService) as jasmine.SpyObj<ExceptionHandlingService>;
    toastrServiceSpy = TestBed.inject(ToastrService) as jasmine.SpyObj<ToastrService>;
    downloadServiceSpy = TestBed.inject(DownloadService) as jasmine.SpyObj<DownloadService>;
    moduleValidationServiceSpy = TestBed.inject(ModuleValidationServiceService) as jasmine.SpyObj<ModuleValidationServiceService>;
    keyValueMappingServiceSpy = TestBed.inject(KeyValueMappingServiceService) as jasmine.SpyObj<KeyValueMappingServiceService>;
    permissionServiceSpy = TestBed.inject(PermissionService) as jasmine.SpyObj<PermissionService>;
    confirmDialogServiceSpy = TestBed.inject(ConfirmDialogService) as jasmine.SpyObj<ConfirmDialogService>;
    updateFeaturesServiceSpy = TestBed.inject(UpdateFeaturesService) as jasmine.SpyObj<UpdateFeaturesService>;
    customerAssociationServiceSpy = TestBed.inject(CustomerAssociationService) as jasmine.SpyObj<CustomerAssociationService>;
    updateProbeTypeServiceSpy = TestBed.inject(UpdateProbeTypeService) as jasmine.SpyObj<UpdateProbeTypeService>;
    deviceServiceSpy = TestBed.inject(DeviceService) as jasmine.SpyObj<DeviceService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Subject Methods', () => {
    it('should return probe list loading subject', () => {
      const subject = service.getProbeListLoadingSubject();
      expect(subject).toBeDefined();
    });

    it('should call probe list loading subject', () => {
      spyOn(service.getProbeListLoadingSubject(), 'next');
      service.callProbeListLoadingSubject(true);
      expect(service.getProbeListLoadingSubject().next).toHaveBeenCalledWith(true);
    });

    it('should return probe detail loading subject', () => {
      const subject = service.getProbeDetailLoadingSubject();
      expect(subject).toBeDefined();
    });

    it('should call probe detail loading subject', () => {
      spyOn(service.getProbeDetailLoadingSubject(), 'next');
      service.callProbeDetailLoadingSubject(false);
      expect(service.getProbeDetailLoadingSubject().next).toHaveBeenCalledWith(false);
    });

    it('should return probe list refresh subject', () => {
      const subject = service.getProbeListRefreshSubject();
      expect(subject).toBeDefined();
    });

    it('should return probe detail refresh subject', () => {
      const subject = service.getProbeDetailRefreshSubject();
      expect(subject).toBeDefined();
    });

    it('should return probe list filter request parameter subject', () => {
      const subject = service.getProbeListFilterRequestParameterSubject();
      expect(subject).toBeDefined();
    });

    it('should call probe list filter request parameter subject', () => {
      const mockFilterAction = new ProbeFilterAction(
        new ListingPageReloadSubjectParameter(true, true, false, false),
        new ProbeListFilterRequestBody(null, null, null, null, null, null, null, null, null, null, null, null, null, null)
      );
      spyOn(service.getProbeListFilterRequestParameterSubject(), 'next');
      service.callProbeListFilterRequestParameterSubject(mockFilterAction);
      expect(service.getProbeListFilterRequestParameterSubject().next).toHaveBeenCalledWith(mockFilterAction);
    });
  });

  describe('Loading Methods', () => {
    it('should call probe list loading subject for ProbListResource', () => {
      spyOn(service, 'callProbeListLoadingSubject');
      service.isLoading(true, ProbListResource);
      expect(service.callProbeListLoadingSubject).toHaveBeenCalledWith(true);
    });

    it('should call probe detail loading subject for ProbDetailResource', () => {
      spyOn(service, 'callProbeDetailLoadingSubject');
      service.isLoading(false, 'ProbDetailResource');
      expect(service.callProbeDetailLoadingSubject).toHaveBeenCalledWith(false);
    });
  });

  describe('Cache Methods', () => {
    it('should set and get sales order number list', async () => {
      const mockSalesOrders = ['SO001', 'SO002'];
      salesOrderApiCallServiceSpy.getSalesOrderNumberList.and.returnValue(Promise.resolve(mockSalesOrders));

      const result = await service.getSalesOrderNumberList();
      expect(result).toEqual(mockSalesOrders);
      expect(salesOrderApiCallServiceSpy.getSalesOrderNumberList).toHaveBeenCalled();
    });

    it('should return cached sales order number list on second call', async () => {
      const mockSalesOrders = ['SO001', 'SO002'];
      service.setSalesOrderNumberList(mockSalesOrders);

      const result = await service.getSalesOrderNumberList();
      expect(result).toEqual(mockSalesOrders);
      expect(salesOrderApiCallServiceSpy.getSalesOrderNumberList).not.toHaveBeenCalled();
    });

    it('should set and get probe types list', async () => {
      const mockProbeTypes = ['Type1', 'Type2'];
      probeApiServiceSpy.getProbeTypesList.and.returnValue(of({ body: mockProbeTypes, status: 200 } as any));

      const result = await service.getProbeTypesList();
      expect(result).toEqual(mockProbeTypes);
    });

    it('should set and get features list', async () => {
      const mockFeatures = [{ featureId: 1, displayName: 'Feature1', partNumbers: [] }] as any;
      const mockFilteredFeatures = [{ id: 1, name: 'Feature1', displayName: 'Feature1', isDisabled: false }] as any;
      probeApiServiceSpy.getFeaturesList.and.returnValue(Promise.resolve(mockFeatures));
      probeServiceSpy.getFeaturesListForFilter.and.returnValue(mockFilteredFeatures);

      const result = await service.getFeaturesList();
      expect(result).toEqual(mockFilteredFeatures);
      expect(probeServiceSpy.getFeaturesListForFilter).toHaveBeenCalledWith(mockFeatures, false);
    });

    it('should set and get country list', async () => {
      const mockCountries = [{ id: 1, name: 'Country1' }] as any;
      countryCacheServiceSpy.getCountryListFromCache.and.returnValue(Promise.resolve(mockCountries));

      const result = await service.getCountryList();
      expect(result).toEqual(mockCountries);
      expect(countryCacheServiceSpy.getCountryListFromCache).toHaveBeenCalledWith(true);
    });
  });

  describe('Filter Validation', () => {
    it('should validate filter form with valid data', () => {
      const formValue = {
        serialNumber: 'SN001',
        probeTypes: ['Type1'],
        probeFeatures: [{ id: 1 }],
        customerName: 'Customer1'
      };

      const result = service.validateProbeFilterForm(formValue);
      expect(result).toBe(true);
    });

    it('should invalidate filter form with no data', () => {
      const formValue = {
        serialNumber: null,
        probeTypes: null,
        probeFeatures: null,
        customerName: null
      };

      const result = service.validateProbeFilterForm(formValue);
      expect(result).toBe(false);
      expect(toastrServiceSpy.info).toHaveBeenCalled();
    });

    it('should invalidate filter form with historical data but missing required fields', () => {
      const formValue = {
        deviceHistoricalData: 'HISTORY',
        deviceModel: null,
        manufacturer: null,
        osType: null
      };

      const result = service.validateProbeFilterForm(formValue);
      expect(result).toBe(false);
      expect(toastrServiceSpy.info).toHaveBeenCalled();
    });
  });

  describe('Probe Operations', () => {
    const mockProbeIds = [1, 2, 3];
    const mockSelectedProbes = [
      { id: 1, editable: true, country: 'US', locked: false },
      { id: 2, editable: true, country: 'US', locked: false },
      { id: 3, editable: true, country: 'US', locked: false }
    ];

    beforeEach(() => {
      // Setup default spy returns
      moduleValidationServiceSpy.validateWithEditableWithMultipalRecoard.and.returnValue(true);
      moduleValidationServiceSpy.validateWithUserCountryForMultileRecord.and.returnValue(true);
      moduleValidationServiceSpy.validateWithEditStateForSingleRecord.and.returnValue(true);
      moduleValidationServiceSpy.validateWithUserCountryForSingleRecord.and.returnValue(true);
      permissionServiceSpy.getProbPermission.and.returnValue(true);
      confirmDialogServiceSpy.confirm.and.returnValue(Promise.resolve(true));
      confirmDialogServiceSpy.getBasicModelConfigForDisableAction.and.returnValue({
        title: 'Confirm', message: 'Are you sure?', btnOkText: 'OK', btnCancelText: 'Cancel'
      });
    });

    describe('lockUnlockProbes', () => {
      it('should successfully lock probes for list resource', async () => {
        probeApiServiceSpy.updateLockState.and.returnValue(Promise.resolve(true));
        spyOn(service, 'isLoading');
        spyOn(service as any, 'showSuccessAndRefresh');

        const result = await service.lockUnlockProbes(mockProbeIds, mockSelectedProbes, true, ProbListResource);

        expect(result).toBe(true);
        expect(probeApiServiceSpy.updateLockState).toHaveBeenCalledWith(mockProbeIds, true);
        expect((service as any).showSuccessAndRefresh).toHaveBeenCalledWith('Probes locked successfully', ProbListResource);
      });

      it('should successfully unlock probes for detail resource', async () => {
        probeApiServiceSpy.updateLockState.and.returnValue(Promise.resolve(true));
        spyOn(service, 'isLoading');
        spyOn(service as any, 'showSuccessAndRefresh');

        const result = await service.lockUnlockProbes([1], [mockSelectedProbes[0]], false, ProbDetailResource);

        expect(result).toBe(true);
        expect(probeApiServiceSpy.updateLockState).toHaveBeenCalledWith([1], false);
        expect((service as any).showSuccessAndRefresh).toHaveBeenCalledWith('Probes unlocked successfully', ProbDetailResource);
      });

      it('should return false when validation fails', async () => {
        spyOn(service as any, 'validateProbeLockUnlockPermissions').and.returnValue(false);

        const result = await service.lockUnlockProbes(mockProbeIds, mockSelectedProbes, true, ProbListResource);

        expect(result).toBe(false);
        expect(probeApiServiceSpy.updateLockState).not.toHaveBeenCalled();
      });

      it('should return false when permission check fails', async () => {
        permissionServiceSpy.getProbPermission.and.returnValue(false);

        const result = await service.lockUnlockProbes(mockProbeIds, mockSelectedProbes, true, ProbListResource);

        expect(result).toBe(false);
        expect(probeApiServiceSpy.updateLockState).not.toHaveBeenCalled();
      });

      it('should handle API errors', async () => {
        const error = new Error('API Error');
        probeApiServiceSpy.updateLockState.and.returnValue(Promise.reject(error));

        const result = await service.lockUnlockProbes(mockProbeIds, mockSelectedProbes, true, ProbListResource);

        expect(result).toBe(false);
        expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalledWith(jasmine.any(Error));
      });
    });

    describe('enableDisableProbes', () => {
      it('should successfully enable probes', async () => {
        probeServiceSpy.probeEditAction.and.returnValue(Promise.resolve());
        spyOn(service, 'isLoading');
        spyOn(service as any, 'showSuccessAndRefresh');

        const result = await service.enableDisableProbes(mockProbeIds, mockSelectedProbes, true, ProbListResource);

        expect(result).toBe(true);
        expect(probeServiceSpy.probeEditAction).toHaveBeenCalledWith(mockProbeIds, true);
        expect((service as any).showSuccessAndRefresh).toHaveBeenCalledWith('Probes enabled successfully', ProbListResource);
      });

      it('should successfully disable probes', async () => {
        probeServiceSpy.probeEditAction.and.returnValue(Promise.resolve());
        spyOn(service, 'isLoading');
        spyOn(service as any, 'showSuccessAndRefresh');

        const result = await service.enableDisableProbes(mockProbeIds, mockSelectedProbes, false, ProbListResource);

        expect(result).toBe(true);
        expect(probeServiceSpy.probeEditAction).toHaveBeenCalledWith(mockProbeIds, false);
        expect((service as any).showSuccessAndRefresh).toHaveBeenCalledWith('Probes disabled successfully', ProbListResource);
      });

      it('should return false when validation fails', async () => {
        spyOn(service as any, 'validateProbeEnableDisablePermissions').and.returnValue(false);

        const result = await service.enableDisableProbes(mockProbeIds, mockSelectedProbes, true, ProbListResource);

        expect(result).toBe(false);
        expect(probeServiceSpy.probeEditAction).not.toHaveBeenCalled();
      });

      it('should handle API errors', async () => {
        const error = new Error('API Error');
        probeServiceSpy.probeEditAction.and.returnValue(Promise.reject(error));

        const result = await service.enableDisableProbes(mockProbeIds, mockSelectedProbes, true, ProbListResource);

        expect(result).toBe(false);
        expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalledWith(jasmine.any(Error));
      });
    });

    describe('deleteProbes', () => {
      it('should successfully delete probes', async () => {
        const mockResponse = new HttpResponse({ body: { message: 'Probes deleted successfully' } as SuccessMessageResponse });
        probeApiServiceSpy.deleteProbes.and.returnValue(of(mockResponse));
        spyOn(service, 'isLoading');
        spyOn(service as any, 'showSuccessAndRefresh');

        const result = await service.deleteProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(result).toBe(true);
        expect(confirmDialogServiceSpy.confirm).toHaveBeenCalled();
        expect(probeApiServiceSpy.deleteProbes).toHaveBeenCalledWith(mockProbeIds);
        expect((service as any).showSuccessAndRefresh).toHaveBeenCalledWith('Probes deleted successfully', ProbListResource);
      });

      it('should return false when validation fails', async () => {
        spyOn(service as any, 'validateProbePermissions').and.returnValue(false);

        const result = await service.deleteProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(result).toBe(false);
        expect(confirmDialogServiceSpy.confirm).not.toHaveBeenCalled();
      });

      it('should return false when user cancels confirmation', async () => {
        confirmDialogServiceSpy.confirm.and.returnValue(Promise.resolve(false));

        const result = await service.deleteProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(result).toBe(false);
        expect(probeApiServiceSpy.deleteProbes).not.toHaveBeenCalled();
      });
    });

    describe('rmaProductStatusForProbes', () => {
      it('should successfully set probes to RMA status', async () => {
        const mockResponse = new HttpResponse({ body: { message: 'Probes set to RMA successfully' } as SuccessMessageResponse });
        probeApiServiceSpy.rmaProductStatusForProbe.and.returnValue(of(mockResponse));
        spyOn(service, 'isLoading');
        spyOn(service as any, 'showSuccessAndRefresh');

        const result = await service.rmaProductStatusForProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(result).toBe(true);
        expect(confirmDialogServiceSpy.confirm).toHaveBeenCalled();
        expect(probeApiServiceSpy.rmaProductStatusForProbe).toHaveBeenCalledWith(mockProbeIds);
        expect((service as any).showSuccessAndRefresh).toHaveBeenCalledWith('Probes set to RMA successfully', ProbListResource);
      });

      it('should return false when validation fails', async () => {
        spyOn(service as any, 'validateProbeRMAPermissions').and.returnValue(false);

        const result = await service.rmaProductStatusForProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(result).toBe(false);
        expect(confirmDialogServiceSpy.confirm).not.toHaveBeenCalled();
      });
    });

    describe('disableProductStatusForProbes', () => {
      it('should successfully disable probes', async () => {
        const mockResponse = new HttpResponse({ body: { message: 'Probes disabled successfully' } as SuccessMessageResponse });
        probeApiServiceSpy.disableProductStatusForProbe.and.returnValue(of(mockResponse));
        spyOn(service, 'isLoading');
        spyOn(service as any, 'showSuccessAndRefresh');

        const result = await service.disableProductStatusForProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(result).toBe(true);
        expect(confirmDialogServiceSpy.confirm).toHaveBeenCalled();
        expect(probeApiServiceSpy.disableProductStatusForProbe).toHaveBeenCalledWith(mockProbeIds);
        expect((service as any).showSuccessAndRefresh).toHaveBeenCalledWith('Probes disabled successfully', ProbListResource);
      });

      it('should return false when permission check fails', async () => {
        permissionServiceSpy.getProbPermission.and.returnValue(false);

        const result = await service.disableProductStatusForProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(result).toBe(false);
        expect(confirmDialogServiceSpy.confirm).not.toHaveBeenCalled();
      });
    });

    describe('associationProbeWithSalesOrder', () => {
      it('should successfully associate probes with sales order', async () => {
        const mockResponse = new HttpResponse({ body: { message: 'Probes associated successfully' } as SuccessMessageResponse });
        const mockCustomerAssociationRequest = {
          button: true,
          basicSalesOrderDetailResponse: { id: 1 } as any,
          isSalesOrderNewAdd: false
        } as CustomerAssociationRequest;

        customerAssociationServiceSpy.openCustomerAssociationPopup.and.returnValue(Promise.resolve(mockCustomerAssociationRequest));
        probeApiServiceSpy.associationProbeWithSalesOrder.and.returnValue(of(mockResponse));
        spyOn(service, 'isLoading');
        spyOn(service as any, 'showSuccessAndRefresh');

        const result = await service.associationProbeWithSalesOrder(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(result).toBe(true);
        expect(customerAssociationServiceSpy.openCustomerAssociationPopup).toHaveBeenCalled();
        expect(probeApiServiceSpy.associationProbeWithSalesOrder).toHaveBeenCalledWith(mockProbeIds, jasmine.any(Object));
        expect((service as any).showSuccessAndRefresh).toHaveBeenCalledWith('Probes associated successfully', ProbListResource);
      });

      it('should return false when user cancels association', async () => {
        const mockCustomerAssociationRequest = { button: false } as CustomerAssociationRequest;
        customerAssociationServiceSpy.openCustomerAssociationPopup.and.returnValue(Promise.resolve(mockCustomerAssociationRequest));

        const result = await service.associationProbeWithSalesOrder(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(result).toBe(false);
        expect(probeApiServiceSpy.associationProbeWithSalesOrder).not.toHaveBeenCalled();
      });
    });

    describe('updateProbeFeatures', () => {
      it('should successfully update probe features for single probe', async () => {
        const mockConfigureLicenceResponse = { button: true } as ConfigureLicenceResponse;
        updateFeaturesServiceSpy.getAssignProbeBasicModelConfigDetail.and.returnValue({
          title: 'Test', message: 'Test', btnOkText: 'OK', btnCancelText: 'Cancel'
        });
        updateFeaturesServiceSpy.openAssignProbeFeatureModel.and.returnValue(Promise.resolve(mockConfigureLicenceResponse));
        spyOn(service, 'isLoading');
        spyOn(service, 'callRefreshPageSubject');

        const result = await service.updateProbeFeatures([1], [mockSelectedProbes[0]], ProbListResource);

        expect(result).toBe(true);
        expect(updateFeaturesServiceSpy.openAssignProbeFeatureModel).toHaveBeenCalled();
        expect(service.callRefreshPageSubject).toHaveBeenCalled();
      });

      it('should return false when multiple probes are selected', async () => {
        const result = await service.updateProbeFeatures(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(result).toBe(false);
        expect(toastrServiceSpy.info).toHaveBeenCalled();
        expect(updateFeaturesServiceSpy.openAssignProbeFeatureModel).not.toHaveBeenCalled();
      });

      it('should return false when permission check fails', async () => {
        permissionServiceSpy.getProbPermission.and.returnValue(false);

        const result = await service.updateProbeFeatures([1], [mockSelectedProbes[0]], ProbListResource);

        expect(result).toBe(false);
        expect(updateFeaturesServiceSpy.openAssignProbeFeatureModel).not.toHaveBeenCalled();
      });
    });

    describe('Validation Methods', () => {
      it('should validate probe permissions for list resource', () => {
        spyOn(service as any, 'validateProbeSelection').and.returnValue(true);

        const result = service.validateProbePermissions(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(result).toBe(true);
        expect((service as any).validateProbeSelection).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, ProbListResource);
      });

      it('should validate probe permissions for detail resource', () => {
        spyOn(service as any, 'validateSingleProbePermissions').and.returnValue(true);

        const result = service.validateProbePermissions([1], [mockSelectedProbes[0]], ProbDetailResource);

        expect(result).toBe(true);
        expect((service as any).validateSingleProbePermissions).toHaveBeenCalledWith(mockSelectedProbes[0], ProbDetailResource);
      });

      it('should return false for invalid resource', () => {
        const result = service.validateProbePermissions(mockProbeIds, mockSelectedProbes, 'InvalidResource');

        expect(result).toBe(false);
      });
    });

    describe('Additional Probe Operations', () => {
      describe('downloadProbes', () => {
        it('should successfully download probes for list resource', async () => {
          spyOn(service as any, 'validateProbePermissions').and.returnValue(true);

          const result = await service.downloadProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

          expect(result).toBe(true);
          expect(deviceServiceSpy.dowloadSasUriofFeatureLicenseConfirmationModel).toHaveBeenCalledWith(ProbListResource);
        });

        it('should successfully download probes for detail resource', async () => {
          spyOn(service as any, 'validateProbePermissions').and.returnValue(true);
          probeApiServiceSpy.dowloadSasUriofFeatureLicenseAsync.and.returnValue(Promise.resolve());

          const result = await service.downloadProbes([1], [mockSelectedProbes[0]], ProbDetailResource);

          expect(result).toBe(true);
          expect(probeApiServiceSpy.dowloadSasUriofFeatureLicenseAsync).toHaveBeenCalledWith([1], ProbDetailResource);
        });

        it('should return false when validation fails', async () => {
          spyOn(service as any, 'validateProbePermissions').and.returnValue(false);

          const result = await service.downloadProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

          expect(result).toBe(false);
          expect(deviceServiceSpy.dowloadSasUriofFeatureLicenseConfirmationModel).not.toHaveBeenCalled();
        });
      });

      describe('updateProbeType', () => {
        it('should successfully update probe type', async () => {
          spyOn(service as any, 'validateProbePermissions').and.returnValue(true);
          updateProbeTypeServiceSpy.openUpdateProbeTypePopup.and.returnValue(Promise.resolve(true));
          spyOn(service, 'isLoading');
          spyOn(service, 'callRefreshPageSubject');

          const result = await service.updateProbeType(mockProbeIds, mockSelectedProbes, 'TestType', ProbListResource);

          expect(result).toBe(true);
          expect(updateProbeTypeServiceSpy.openUpdateProbeTypePopup).toHaveBeenCalled();
          expect(service.callRefreshPageSubject).toHaveBeenCalled();
        });

        it('should return false when user cancels update', async () => {
          spyOn(service as any, 'validateProbePermissions').and.returnValue(true);
          updateProbeTypeServiceSpy.openUpdateProbeTypePopup.and.returnValue(Promise.resolve(false));

          const result = await service.updateProbeType(mockProbeIds, mockSelectedProbes, null, ProbListResource);

          expect(result).toBe(false);
        });
      });

      describe('exportProbesCSV', () => {
        it('should successfully export probes to CSV', async () => {
          spyOn(service as any, 'validateProbePermissions').and.returnValue(true);
          spyOn(service as any, 'getParameterForDeviceHistoricalData').and.returnValue('normal');
          const mockGenerateResponse = new HttpResponse({ body: { fileName: 'test.csv' } });
          const mockDownloadResponse = new Blob(['test data']);

          probeApiServiceSpy.generateCSVFileForProbe.and.returnValue(of(mockGenerateResponse));
          probeApiServiceSpy.downloadCSVFileForProbe.and.returnValue(of(mockDownloadResponse));
          spyOn(service, 'isLoading');
          spyOn(service as any, 'showSuccessAndRefresh');

          const result = await service.exportProbesCSV(mockProbeIds, mockSelectedProbes, ProbListResource);

          expect(result).toBe(true);
          expect(probeApiServiceSpy.generateCSVFileForProbe).toHaveBeenCalled();
          expect(probeApiServiceSpy.downloadCSVFileForProbe).toHaveBeenCalledWith('test.csv');
          expect(downloadServiceSpy.downloadExportCSV).toHaveBeenCalledWith("List_of_Probe(s).xls", mockDownloadResponse);
        });

        it('should return false when validation fails', async () => {
          spyOn(service as any, 'validateProbePermissions').and.returnValue(false);

          const result = await service.exportProbesCSV(mockProbeIds, mockSelectedProbes, ProbListResource);

          expect(result).toBe(false);
          expect(probeApiServiceSpy.generateCSVFileForProbe).not.toHaveBeenCalled();
        });
      });

      describe('exportProbeHistoricalConnections', () => {
        it('should successfully export historical connections', async () => {
          const mockGenerateResponse = new HttpResponse({ body: { fileName: 'history.csv' } });
          const mockDownloadResponse = new Blob(['history data']);

          probeApiServiceSpy.generateCSVFileForProbeHistoricalConnection.and.returnValue(of(mockGenerateResponse));
          probeApiServiceSpy.downloadCSVFileForProbe.and.returnValue(of(mockDownloadResponse));
          spyOn(service, 'isLoading');

          const result = await service.exportProbeHistoricalConnections(1, 'TEST123', ProbDetailResource);

          expect(result).toBe(true);
          expect(probeApiServiceSpy.generateCSVFileForProbeHistoricalConnection).toHaveBeenCalled();
          expect(downloadServiceSpy.downloadExportCSV).toHaveBeenCalledWith('TEST123_Probe_Connection_History.xls', mockDownloadResponse);
        });

        it('should handle API errors', async () => {
          const error = new Error('API Error');
          probeApiServiceSpy.generateCSVFileForProbeHistoricalConnection.and.returnValue(throwError(() => error));
          spyOn(service, 'isLoading');

          const result = await service.exportProbeHistoricalConnections(1, 'TEST123', ProbDetailResource);

          expect(result).toBe(false);
          expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalledWith(jasmine.any(Error));
        });
      });

      describe('transferProbe', () => {
        it('should successfully initiate probe transfer', async () => {
          const mockProbeDetail = {
            serialNumber: 'TEST123',
            productStatus: 'ENABLED',
            salesOrderId: 1,
            type: 'TestType'
          };
          spyOn(service as any, 'validateSingleProbePermissions').and.returnValue(true);

          const result = await service.transferProbe(1, mockProbeDetail, ProbDetailResource);

          expect(result).toBe(true);
          expect(toastrServiceSpy.success).toHaveBeenCalledWith('Transfer probe functionality initiated');
        });

        it('should return false when serial number is null', async () => {
          const mockProbeDetail = { serialNumber: null };
          spyOn(service as any, 'validateSingleProbePermissions').and.returnValue(true);

          const result = await service.transferProbe(1, mockProbeDetail, ProbDetailResource);

          expect(result).toBe(false);
          expect(toastrServiceSpy.info).toHaveBeenCalled();
        });

        it('should return false when validation fails', async () => {
          const mockProbeDetail = { serialNumber: 'TEST123' };
          spyOn(service as any, 'validateSingleProbePermissions').and.returnValue(false);

          const result = await service.transferProbe(1, mockProbeDetail, ProbDetailResource);

          expect(result).toBe(false);
        });
      });
    });
  });
});
