import { TestBed } from '@angular/core/testing';
import { ToastrService } from 'ngx-toastr';
import { CommonOperationsService } from './common-operations.service';
import { RoleApiCallService } from '../Service/RoleService/role-api-call.service';
import { CreateUpdateRoleService } from '../modalservice/create-update-role.service';
import { PermissionService } from '../permission.service';
import { ImportCsvfileService } from '../modalservice/import-csvfile.service';
import { KitManagemantService } from '../Service/KitManagementService/kit-managemant.service';
import { OtsKitManagemantService } from '../Service/OtsKitManagementService/ots-kit-managemant.service';
import { DeviceOperationService } from '../../FeatureModule/Device/DeviceService/Device-Operation/device-operation.service';
import { ProbeOperationService } from '../../FeatureModule/Probe/ProbeService/Probe-Operation/probe-operation.service';
import { CustomerAssociationService } from '../modalservice/customer-association.service';
import { ProbListResource, ProbDetailResource, Probe_Select_Message } from '../../app.constants';
import { ProbeOperationsEnum } from '../enum/Operations/ProbeOperations.enum';

describe('CommonOperationsService', () => {
  let service: CommonOperationsService;
  let toastrServiceSpy: jasmine.SpyObj<ToastrService>;
  let roleApiCallServiceSpy: jasmine.SpyObj<RoleApiCallService>;
  let createUpdateRoleServiceSpy: jasmine.SpyObj<CreateUpdateRoleService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let importCsvfileServiceSpy: jasmine.SpyObj<ImportCsvfileService>;
  let kitManagemantServiceSpy: jasmine.SpyObj<KitManagemantService>;
  let otsKitManagemantServiceSpy: jasmine.SpyObj<OtsKitManagemantService>;
  let deviceOperationServiceSpy: jasmine.SpyObj<DeviceOperationService>;
  let probeOperationServiceSpy: jasmine.SpyObj<ProbeOperationService>;
  let customerAssociationServiceSpy: jasmine.SpyObj<CustomerAssociationService>;

  beforeEach(() => {
    const toastrSpy = jasmine.createSpyObj('ToastrService', ['info', 'success', 'error']);
    const roleApiSpy = jasmine.createSpyObj('RoleApiCallService', ['getAllRoles']);
    const createUpdateRoleSpy = jasmine.createSpyObj('CreateUpdateRoleService', ['openCreateUpdateRoleModel']);
    const permissionSpy = jasmine.createSpyObj('PermissionService', ['getRolePermission']);
    const importCsvSpy = jasmine.createSpyObj('ImportCsvfileService', ['openImportCsvFileModel']);
    const kitManagementSpy = jasmine.createSpyObj('KitManagemantService', ['getAllKits']);
    const otsKitManagementSpy = jasmine.createSpyObj('OtsKitManagemantService', ['getAllOtsKits']);
    const deviceOperationSpy = jasmine.createSpyObj('DeviceOperationService', ['lockUnlockDevices']);
    const probeOperationSpy = jasmine.createSpyObj('ProbeOperationService', [
      'lockUnlockProbes', 'enableDisableProbes', 'deleteProbes', 'rmaProductStatusForProbes',
      'disableProductStatusForProbes', 'associationProbeWithSalesOrder', 'updateProbeFeatures',
      'downloadProbes', 'updateProbeType', 'exportProbesCSV', 'exportProbeHistoricalConnections',
      'transferProbe', 'isLoading'
    ]);
    const customerAssociationSpy = jasmine.createSpyObj('CustomerAssociationService', ['openCustomerAssociationPopup']);

    TestBed.configureTestingModule({
      providers: [
        CommonOperationsService,
        { provide: ToastrService, useValue: toastrSpy },
        { provide: RoleApiCallService, useValue: roleApiSpy },
        { provide: CreateUpdateRoleService, useValue: createUpdateRoleSpy },
        { provide: PermissionService, useValue: permissionSpy },
        { provide: ImportCsvfileService, useValue: importCsvSpy },
        { provide: KitManagemantService, useValue: kitManagementSpy },
        { provide: OtsKitManagemantService, useValue: otsKitManagementSpy },
        { provide: DeviceOperationService, useValue: deviceOperationSpy },
        { provide: ProbeOperationService, useValue: probeOperationSpy },
        { provide: CustomerAssociationService, useValue: customerAssociationSpy }
      ]
    });

    service = TestBed.inject(CommonOperationsService);
    toastrServiceSpy = TestBed.inject(ToastrService) as jasmine.SpyObj<ToastrService>;
    roleApiCallServiceSpy = TestBed.inject(RoleApiCallService) as jasmine.SpyObj<RoleApiCallService>;
    createUpdateRoleServiceSpy = TestBed.inject(CreateUpdateRoleService) as jasmine.SpyObj<CreateUpdateRoleService>;
    permissionServiceSpy = TestBed.inject(PermissionService) as jasmine.SpyObj<PermissionService>;
    importCsvfileServiceSpy = TestBed.inject(ImportCsvfileService) as jasmine.SpyObj<ImportCsvfileService>;
    kitManagemantServiceSpy = TestBed.inject(KitManagemantService) as jasmine.SpyObj<KitManagemantService>;
    otsKitManagemantServiceSpy = TestBed.inject(OtsKitManagemantService) as jasmine.SpyObj<OtsKitManagemantService>;
    deviceOperationServiceSpy = TestBed.inject(DeviceOperationService) as jasmine.SpyObj<DeviceOperationService>;
    probeOperationServiceSpy = TestBed.inject(ProbeOperationService) as jasmine.SpyObj<ProbeOperationService>;
    customerAssociationServiceSpy = TestBed.inject(CustomerAssociationService) as jasmine.SpyObj<CustomerAssociationService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('changeOperationForProbe', () => {
    const mockProbeIds = [1, 2, 3];
    const mockSelectedProbes = [
      { id: 1, editable: true, country: 'US' },
      { id: 2, editable: true, country: 'US' },
      { id: 3, editable: true, country: 'US' }
    ];

    beforeEach(() => {
      // Setup default spy returns
      probeOperationServiceSpy.lockUnlockProbes.and.returnValue(Promise.resolve(true));
      probeOperationServiceSpy.enableDisableProbes.and.returnValue(Promise.resolve(true));
      probeOperationServiceSpy.deleteProbes.and.returnValue(Promise.resolve(true));
      probeOperationServiceSpy.rmaProductStatusForProbes.and.returnValue(Promise.resolve(true));
      probeOperationServiceSpy.disableProductStatusForProbes.and.returnValue(Promise.resolve(true));
      probeOperationServiceSpy.associationProbeWithSalesOrder.and.returnValue(Promise.resolve(true));
      probeOperationServiceSpy.updateProbeFeatures.and.returnValue(Promise.resolve(true));
      probeOperationServiceSpy.downloadProbes.and.returnValue(Promise.resolve(true));
      probeOperationServiceSpy.updateProbeType.and.returnValue(Promise.resolve(true));
      probeOperationServiceSpy.exportProbesCSV.and.returnValue(Promise.resolve(true));
      probeOperationServiceSpy.exportProbeHistoricalConnections.and.returnValue(Promise.resolve(true));
      probeOperationServiceSpy.transferProbe.and.returnValue(Promise.resolve(true));
    });

    it('should show info message when no probes are selected', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.LOCK_PROBES, ProbListResource, [], []);

      expect(toastrServiceSpy.info).toHaveBeenCalledWith(Probe_Select_Message);
      expect(probeOperationServiceSpy.lockUnlockProbes).not.toHaveBeenCalled();
    });

    it('should call lockUnlockProbes with true for LOCK_PROBES operation', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.LOCK_PROBES, ProbListResource, mockProbeIds, mockSelectedProbes);

      expect(probeOperationServiceSpy.isLoading).toHaveBeenCalledWith(true, ProbListResource);
      expect(probeOperationServiceSpy.lockUnlockProbes).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, true, ProbListResource);
    });

    it('should call lockUnlockProbes with false for UNLOCK_PROBES operation', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.UNLOCK_PROBES, ProbListResource, mockProbeIds, mockSelectedProbes);

      expect(probeOperationServiceSpy.isLoading).toHaveBeenCalledWith(true, ProbListResource);
      expect(probeOperationServiceSpy.lockUnlockProbes).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, false, ProbListResource);
    });

    it('should call enableDisableProbes with true for EDIT_ENABLE_PROBE operation', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.EDIT_ENABLE_PROBE, ProbDetailResource, mockProbeIds, mockSelectedProbes);

      expect(probeOperationServiceSpy.isLoading).toHaveBeenCalledWith(true, ProbDetailResource);
      expect(probeOperationServiceSpy.enableDisableProbes).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, true, ProbDetailResource);
    });

    it('should call enableDisableProbes with false for EDIT_DISABLE_PROBE operation', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.EDIT_DISABLE_PROBE, ProbListResource, mockProbeIds, mockSelectedProbes);

      expect(probeOperationServiceSpy.isLoading).toHaveBeenCalledWith(true, ProbListResource);
      expect(probeOperationServiceSpy.enableDisableProbes).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, false, ProbListResource);
    });

    it('should call deleteProbes for DELETE_PROBES operation', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.DELETE_PROBES, ProbListResource, mockProbeIds, mockSelectedProbes);

      expect(probeOperationServiceSpy.deleteProbes).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, ProbListResource);
    });

    it('should call rmaProductStatusForProbes for RMA_PROBE operation', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.RMA_PROBE, ProbDetailResource, mockProbeIds, mockSelectedProbes);

      expect(probeOperationServiceSpy.rmaProductStatusForProbes).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, ProbDetailResource);
    });

    it('should call disableProductStatusForProbes for DISABLED_PROBE operation', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.DISABLED_PROBE, ProbListResource, mockProbeIds, mockSelectedProbes);

      expect(probeOperationServiceSpy.disableProductStatusForProbes).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, ProbListResource);
    });

    it('should call associationProbeWithSalesOrder for CUSTOMER_ASSOCIATION operation', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.CUSTOMER_ASSOCIATION, ProbDetailResource, mockProbeIds, mockSelectedProbes);

      expect(probeOperationServiceSpy.associationProbeWithSalesOrder).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, ProbDetailResource);
    });

    it('should call updateProbeFeatures for ASSIGN_FEATURES_TO_PROBE operation', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.ASSIGN_FEATURES_TO_PROBE, ProbListResource, mockProbeIds, mockSelectedProbes);

      expect(probeOperationServiceSpy.updateProbeFeatures).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, ProbListResource);
    });

    it('should call downloadProbes for DOWNLOAD_PROBES operation', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.DOWNLOAD_PROBES, ProbListResource, mockProbeIds, mockSelectedProbes);

      expect(probeOperationServiceSpy.downloadProbes).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, ProbListResource);
    });

    it('should call updateProbeType for UPDATE_PROBE_TYPE operation with null type for list resource', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.UPDATE_PROBE_TYPE, ProbListResource, mockProbeIds, mockSelectedProbes);

      expect(probeOperationServiceSpy.updateProbeType).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, null, ProbListResource);
    });

    it('should call updateProbeType for UPDATE_PROBE_TYPE operation with current type for detail resource', () => {
      const mockProbesWithType = [{ ...mockSelectedProbes[0], type: 'TestType' }];
      service.changeOperationForProbe(ProbeOperationsEnum.UPDATE_PROBE_TYPE, ProbDetailResource, [1], mockProbesWithType);

      expect(probeOperationServiceSpy.updateProbeType).toHaveBeenCalledWith([1], mockProbesWithType, 'TestType', ProbDetailResource);
    });

    it('should call exportProbesCSV for Export_CSV operation with loading management', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.Export_CSV, ProbListResource, mockProbeIds, mockSelectedProbes);

      expect(probeOperationServiceSpy.isLoading).toHaveBeenCalledWith(true, ProbListResource);
      expect(probeOperationServiceSpy.exportProbesCSV).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, ProbListResource);
    });

    it('should call exportProbeHistoricalConnections for EXPORT_HISTORICAL_CONNECTIONS operation for detail resource', () => {
      const mockProbesWithSerial = [{ ...mockSelectedProbes[0], serialNumber: 'TEST123' }];
      service.changeOperationForProbe(ProbeOperationsEnum.EXPORT_HISTORICAL_CONNECTIONS, ProbDetailResource, [1], mockProbesWithSerial);

      expect(probeOperationServiceSpy.exportProbeHistoricalConnections).toHaveBeenCalledWith(1, 'TEST123', ProbDetailResource);
    });

    it('should not call exportProbeHistoricalConnections for EXPORT_HISTORICAL_CONNECTIONS operation for list resource', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.EXPORT_HISTORICAL_CONNECTIONS, ProbListResource, mockProbeIds, mockSelectedProbes);

      expect(probeOperationServiceSpy.exportProbeHistoricalConnections).not.toHaveBeenCalled();
    });

    it('should call transferProbe for TRANSFER_PROBE operation for detail resource', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.TRANSFER_PROBE, ProbDetailResource, [1], [mockSelectedProbes[0]]);

      expect(probeOperationServiceSpy.transferProbe).toHaveBeenCalledWith(1, mockSelectedProbes[0], ProbDetailResource);
    });

    it('should not call transferProbe for TRANSFER_PROBE operation for list resource', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.TRANSFER_PROBE, ProbListResource, mockProbeIds, mockSelectedProbes);

      expect(probeOperationServiceSpy.transferProbe).not.toHaveBeenCalled();
    });

    it('should handle unknown operation gracefully', () => {
      service.changeOperationForProbe('UNKNOWN_OPERATION', ProbListResource, mockProbeIds, mockSelectedProbes);

      // Should not call any probe operation methods
      expect(probeOperationServiceSpy.lockUnlockProbes).not.toHaveBeenCalled();
      expect(probeOperationServiceSpy.enableDisableProbes).not.toHaveBeenCalled();
      expect(probeOperationServiceSpy.deleteProbes).not.toHaveBeenCalled();
    });

    it('should set loading to false after operation completes successfully', async () => {
      const lockPromise = Promise.resolve(true);
      probeOperationServiceSpy.lockUnlockProbes.and.returnValue(lockPromise);

      service.changeOperationForProbe(ProbeOperationsEnum.LOCK_PROBES, ProbListResource, mockProbeIds, mockSelectedProbes);

      await lockPromise;
      expect(probeOperationServiceSpy.isLoading).toHaveBeenCalledWith(false, ProbListResource);
    });

    it('should set loading to false after operation fails', async () => {
      const lockPromise = Promise.reject(new Error('Operation failed'));
      probeOperationServiceSpy.lockUnlockProbes.and.returnValue(lockPromise);

      service.changeOperationForProbe(ProbeOperationsEnum.LOCK_PROBES, ProbListResource, mockProbeIds, mockSelectedProbes);

      try {
        await lockPromise;
      } catch (error) {
        // Expected to fail
      }
      expect(probeOperationServiceSpy.isLoading).toHaveBeenCalledWith(false, ProbListResource);
    });
  });
});
